from litellm import acompletion
import json
from logger.log import logger, LogDB, trace_context, set_trace_id, clear_trace_id
import time
from ..utils.utils import log_memory_usage
import os
from prompts import PROMPTS
from ..tools.tools_list import get as get_tools_list


MESSAGE_DELIMITER = "<__!!__END__!!__>"

@logger.catch()
async def chat_stream(llm_: dict, messages: list, call_for: str = "chat"):
    """
    Handle chat streaming requests.
    
    llm_ :: Dictionary containing LLM configuration. => {
        "base_url": str,  # Base URL for the LLM API
        "api_key": str,  # API key for authentication | Default => session_id for CodeMate Cloud Calls.
        "model": str,  # Model name to use
    }
    messages :: List of messages in the chat.
    messages format :: {
        "role": str, # system, user, assistant, tool_call
        "content": str | dict # Content of the message, can be a string or a dict containing string and image base64,
        "context": list | None # Optional, list of contexts to use for the message
    }
    tools :: List of tools available for the LLM to use.
    """

    logger.info(f"[stream] Starting chat stream for {llm_.get('model')} with {len(messages)} messages")

    SYSTEM_PROMPT = await PROMPTS.get(call_for)
    sequence_messages = [{
        "role": "system",
        "content": SYSTEM_PROMPT
    }]
    sequence_messages.extend(messages)
    # prepare tools
    contexts = []
    for message in sequence_messages:
        if message["role"] == "user":
            if "context" in message:
                contexts.append(message["context"])
                del message["context"]
    tools = get_tools_list(contexts)

    iteration = 0
    logger.info(f"[stream] Starting chat stream for {llm_.get('model')}")

    while True:
        iteration += 1
        response = await acompletion(
            base_url=llm_.get("base_url", "https://llm.codemate.ai/v1"),
            api_key=llm_.get("api_key", ""),
            model=llm_.get("model"),
            messages=sequence_messages,
            tools=tools if len(tools) != 0 else None,
            tool_choice="auto" if len(tools) != 0 else None,
            temperature=0.4,
            stream=True
        )

        chunks = []
        tool_calls = {}
        current_content = ""

        async for index, chunk in enumerate(response):
            # content_block = chunk.choices[0].delta.model_dump()
            if chunk.choices[0].delta.tool_calls:
                for tool_call in chunk.choices[0].delta.tool_calls:
                    tool_call_index = tool_call.index
                    if tool_call_index not in tool_calls:
                        tool_calls[tool_call_index] = {"name": "", "args": "", "id": ""}

                    if tool_call.id:
                        tool_calls[tool_call_index]["id"] = tool_call.id

                    if tool_call.function.name:
                        tool_calls[tool_call_index]["name"] += tool_call.function.name
                    
                    if tool_call.function.arguments:
                        tool_calls[tool_call_index]["args"] += tool_call.function.arguments

            if chunk.choices[0].delta.content:
                current_content += chunk.choices[0].delta.content
                yield json.dumps({
                    "index": index,
                    "type": "message",
                    "message": chunk.choices[0].delta.content
                }) + MESSAGE_DELIMITER

        if current_content.strip() and not tool_calls:
            logger.info(f"[stream] Conversation completed after {iteration} iterations")
            break


        if tool_calls:
            logger.info(f"[stream] Processing {len(tool_calls)} tool calls")

            for tool_call_index, func_data in tool_calls.items():
                if func_data["name"] and func_data["args"]:
                    yield json.dumps({
                        "index": tool_call_index,
                        "type": "tool_call",
                        "tool_call": {
                            "name": func_data["name"],
                            "arguments": func_data["args"]
                        }
                    }) + MESSAGE_DELIMITER

            # Break after processing tool calls to avoid infinite loop
            break

        # If no content and no tool calls, break to avoid infinite loop
        if not current_content.strip() and not tool_calls:
            logger.info(f"[stream] No content or tool calls, ending conversation")
            break

    logger.debug(message=f"Chat stream completed for {llm_.get('model')}", extra={
        "_log_": "chat_stream",
        "messages": messages,
        "llm_": llm_,
        "chunks_returned": chunks
    })